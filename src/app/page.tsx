'use client';

import { useState } from 'react';
import { Fund, Strategy, StrategyParams, BacktestResult } from '@/types/fund';
import { getStrategy } from '@/lib/strategies';
import { BacktestEngine } from '@/lib/backtest';
import { getFundData, getIndexData, getFundById } from '@/lib/mockData';
import FundSelector from '@/components/FundSelector';
import StrategySelector from '@/components/StrategySelector';
import ParameterInputs from '@/components/ParameterInputs';
import BacktestChart from '@/components/BacktestChart';

export default function Home() {
  const [selectedFund, setSelectedFund] = useState<Fund | null>(null);
  const [selectedStrategy, setSelectedStrategy] = useState<Strategy | null>(null);
  const [parameters, setParameters] = useState<Record<string, any>>({});
  const [backtestResult, setBacktestResult] = useState<BacktestResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 执行回测
  const runBacktest = async () => {
    if (!selectedFund || !selectedStrategy) {
      setError('请选择基金和投资策略');
      return;
    }

    // 验证必填参数
    const requiredParams = Object.entries(selectedStrategy.parameterSchema)
      .filter(([_, param]) => param.required)
      .map(([key, _]) => key);

    const missingParams = requiredParams.filter(key => !parameters[key] || parameters[key] === '');
    if (missingParams.length > 0) {
      setError('请填写所有必填参数');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // 获取基金数据
      const fundData = await getFundData(
        selectedFund.id,
        parameters.startDate,
        parameters.endDate
      );

      // 获取指数数据（如果基金关联了指数）
      let indexData;
      if (selectedFund.indexId) {
        indexData = await getIndexData(
          selectedFund.indexId,
          parameters.startDate,
          parameters.endDate
        );
      }

      // 创建回测引擎并执行回测
      const engine = new BacktestEngine(fundData, indexData);
      const result = await engine.runBacktest(selectedFund, parameters as StrategyParams);

      setBacktestResult(result);
    } catch (err) {
      console.error('回测执行失败:', err);
      setError('回测执行失败，请检查参数设置');
    } finally {
      setIsLoading(false);
    }
  };

  // 重置所有状态
  const resetAll = () => {
    setSelectedFund(null);
    setSelectedStrategy(null);
    setParameters({});
    setBacktestResult(null);
    setError(null);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">基金投资策略回测计算器</h1>
              <p className="mt-2 text-gray-600">选择基金和投资策略，分析历史表现</p>
            </div>
            <button
              onClick={resetAll}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              重置
            </button>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 左侧配置面板 */}
          <div className="lg:col-span-1 space-y-6">
            {/* 基金选择 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <FundSelector
                selectedFund={selectedFund}
                onFundSelect={setSelectedFund}
              />
            </div>

            {/* 策略选择 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <StrategySelector
                selectedStrategy={selectedStrategy}
                onStrategySelect={setSelectedStrategy}
              />
            </div>

            {/* 参数设置 */}
            {selectedStrategy && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <ParameterInputs
                  strategy={selectedStrategy}
                  parameters={parameters}
                  onParametersChange={setParameters}
                />
              </div>
            )}

            {/* 执行回测按钮 */}
            {selectedFund && selectedStrategy && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <button
                  onClick={runBacktest}
                  disabled={isLoading}
                  className={`w-full py-3 px-4 rounded-lg font-medium text-white transition-colors ${
                    isLoading
                      ? 'bg-gray-400 cursor-not-allowed'
                      : 'bg-blue-600 hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
                  }`}
                >
                  {isLoading ? (
                    <div className="flex items-center justify-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      执行回测中...
                    </div>
                  ) : (
                    '开始回测'
                  )}
                </button>

                {error && (
                  <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                    <div className="flex items-center">
                      <svg className="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                      </svg>
                      <span className="text-sm text-red-800">{error}</span>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* 右侧结果展示 */}
          <div className="lg:col-span-2">
            {backtestResult ? (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <BacktestChart result={backtestResult} />
              </div>
            ) : (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="text-center py-12">
                  <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                  <h3 className="mt-2 text-sm font-medium text-gray-900">暂无回测结果</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    请选择基金和投资策略，设置参数后开始回测
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}
