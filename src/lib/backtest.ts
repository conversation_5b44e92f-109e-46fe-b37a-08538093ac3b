import { 
  BacktestResult, 
  StrategyParams, 
  FundData, 
  IndexData,
  Fund,
  FixedAmountParams,
  ValueAveragingParams,
  SmartFixedParams,
  GridTradingParams,
  MomentumParams,
  MeanReversionParams
} from '@/types/fund';

// 回测引擎类
export class BacktestEngine {
  private fundData: FundData[];
  private indexData?: IndexData[];

  constructor(fundData: FundData[], indexData?: IndexData[]) {
    this.fundData = fundData.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
    this.indexData = indexData?.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  }

  // 执行回测
  async runBacktest(fund: Fund, params: StrategyParams): Promise<BacktestResult> {
    const timeline = this.calculateTimeline(params);
    const performance = this.calculatePerformance(timeline);

    return {
      strategy: this.getStrategyType(params),
      fund,
      params,
      performance,
      timeline
    };
  }

  // 计算时间线数据
  private calculateTimeline(params: StrategyParams) {
    const startDate = new Date(params.startDate);
    const endDate = new Date(params.endDate);
    const timeline: BacktestResult['timeline'] = [];

    let totalInvestment = 0;
    let totalShares = 0;
    let currentDate = new Date(startDate);

    // 初始投资
    if (params.initialAmount > 0) {
      const initialNav = this.getNavOnDate(currentDate);
      if (initialNav) {
        totalInvestment = params.initialAmount;
        totalShares = params.initialAmount / initialNav.netAssetValue;
        
        timeline.push({
          date: currentDate.toISOString().split('T')[0],
          investment: params.initialAmount,
          totalInvestment,
          shares: totalShares,
          value: totalShares * initialNav.netAssetValue,
          return: 0,
          netAssetValue: initialNav.netAssetValue
        });
      }
    }

    // 根据策略类型计算后续投资
    while (currentDate <= endDate) {
      const investment = this.calculateInvestmentAmount(params, currentDate, timeline);
      
      if (investment > 0) {
        const nav = this.getNavOnDate(currentDate);
        if (nav) {
          const newShares = investment / nav.netAssetValue;
          totalShares += newShares;
          totalInvestment += investment;
          
          const currentValue = totalShares * nav.netAssetValue;
          const returnRate = totalInvestment > 0 ? ((currentValue - totalInvestment) / totalInvestment) * 100 : 0;

          timeline.push({
            date: currentDate.toISOString().split('T')[0],
            investment,
            totalInvestment,
            shares: totalShares,
            value: currentValue,
            return: returnRate,
            netAssetValue: nav.netAssetValue
          });
        }
      }

      // 移动到下一个投资日期
      currentDate = this.getNextInvestmentDate(currentDate, params);
    }

    return timeline;
  }

  // 根据策略计算投资金额
  private calculateInvestmentAmount(
    params: StrategyParams, 
    date: Date, 
    timeline: BacktestResult['timeline']
  ): number {
    const strategyType = this.getStrategyType(params);

    switch (strategyType) {
      case 'fixed_amount':
        return this.calculateFixedAmountInvestment(params as FixedAmountParams, date, timeline);
      
      case 'value_averaging':
        return this.calculateValueAveragingInvestment(params as ValueAveragingParams, date, timeline);
      
      case 'smart_fixed':
        return this.calculateSmartFixedInvestment(params as SmartFixedParams, date, timeline);
      
      case 'grid_trading':
        return this.calculateGridTradingInvestment(params as GridTradingParams, date, timeline);
      
      case 'momentum':
        return this.calculateMomentumInvestment(params as MomentumParams, date, timeline);
      
      case 'mean_reversion':
        return this.calculateMeanReversionInvestment(params as MeanReversionParams, date, timeline);
      
      default:
        return 0;
    }
  }

  // 定投策略计算
  private calculateFixedAmountInvestment(
    params: FixedAmountParams, 
    date: Date, 
    timeline: BacktestResult['timeline']
  ): number {
    // 跳过初始投资日期
    if (timeline.length === 0 || date.getTime() === new Date(params.startDate).getTime()) {
      return 0;
    }
    return params.monthlyAmount;
  }

  // 价值平均策略计算
  private calculateValueAveragingInvestment(
    params: ValueAveragingParams, 
    date: Date, 
    timeline: BacktestResult['timeline']
  ): number {
    if (timeline.length === 0) return 0;

    const lastEntry = timeline[timeline.length - 1];
    const monthsElapsed = this.getMonthsBetween(new Date(params.startDate), date);
    const monthlyGrowthRate = params.targetGrowthRate / 12 / 100;
    
    const targetValue = params.initialAmount * Math.pow(1 + monthlyGrowthRate, monthsElapsed);
    const currentValue = lastEntry.value;
    
    const requiredInvestment = targetValue - currentValue;
    return Math.min(Math.max(requiredInvestment, 0), params.maxInvestment);
  }

  // 智能定投策略计算
  private calculateSmartFixedInvestment(
    params: SmartFixedParams, 
    date: Date, 
    timeline: BacktestResult['timeline']
  ): number {
    if (timeline.length === 0) return 0;

    const valuationMultiplier = this.getValuationMultiplier(params.valuationMetric, date);
    return params.baseAmount * valuationMultiplier * params.adjustmentFactor;
  }

  // 网格交易策略计算
  private calculateGridTradingInvestment(
    params: GridTradingParams, 
    date: Date, 
    timeline: BacktestResult['timeline']
  ): number {
    // 简化实现：根据当前价格位置决定是否投资
    const nav = this.getNavOnDate(date);
    if (!nav) return 0;

    // 这里需要更复杂的网格逻辑，暂时简化
    return params.investmentPerGrid;
  }

  // 动量策略计算
  private calculateMomentumInvestment(
    params: MomentumParams, 
    date: Date, 
    timeline: BacktestResult['timeline']
  ): number {
    const momentum = this.calculateMomentum(date, params.lookbackPeriod);
    if (momentum > params.threshold) {
      return 2000; // 上涨动量，增加投资
    } else if (momentum < -params.threshold) {
      return 500;  // 下跌动量，减少投资
    }
    return 1000; // 中性动量，正常投资
  }

  // 均值回归策略计算
  private calculateMeanReversionInvestment(
    params: MeanReversionParams, 
    date: Date, 
    timeline: BacktestResult['timeline']
  ): number {
    const deviation = this.calculateDeviationFromMA(date, params.movingAveragePeriod);
    if (deviation < -params.deviationThreshold) {
      return 2000; // 低于均值，增加投资
    } else if (deviation > params.deviationThreshold) {
      return 500;  // 高于均值，减少投资
    }
    return 1000; // 接近均值，正常投资
  }

  // 辅助方法
  private getStrategyType(params: StrategyParams): string {
    if ('monthlyAmount' in params) return 'fixed_amount';
    if ('targetGrowthRate' in params) return 'value_averaging';
    if ('baseAmount' in params) return 'smart_fixed';
    if ('gridCount' in params) return 'grid_trading';
    if ('lookbackPeriod' in params) return 'momentum';
    if ('movingAveragePeriod' in params) return 'mean_reversion';
    return 'fixed_amount';
  }

  private getNavOnDate(date: Date): FundData | null {
    const dateStr = date.toISOString().split('T')[0];
    return this.fundData.find(d => d.date === dateStr) || null;
  }

  private getNextInvestmentDate(currentDate: Date, params: StrategyParams): Date {
    const frequency = 'frequency' in params ? params.frequency : 'monthly';
    const nextDate = new Date(currentDate);
    
    switch (frequency) {
      case 'weekly':
        nextDate.setDate(nextDate.getDate() + 7);
        break;
      case 'monthly':
        nextDate.setMonth(nextDate.getMonth() + 1);
        break;
      case 'quarterly':
        nextDate.setMonth(nextDate.getMonth() + 3);
        break;
    }
    
    return nextDate;
  }

  private getMonthsBetween(startDate: Date, endDate: Date): number {
    return (endDate.getFullYear() - startDate.getFullYear()) * 12 + 
           (endDate.getMonth() - startDate.getMonth());
  }

  private getValuationMultiplier(metric: string, date: Date): number {
    // 简化实现，实际应该根据指数数据计算
    return Math.random() * 0.5 + 0.75; // 0.75-1.25之间的随机数
  }

  private calculateMomentum(date: Date, lookbackPeriod: number): number {
    // 简化实现，计算价格动量
    const endDate = new Date(date);
    const startDate = new Date(date);
    startDate.setDate(startDate.getDate() - lookbackPeriod);
    
    const endNav = this.getNavOnDate(endDate);
    const startNav = this.getNavOnDate(startDate);
    
    if (!endNav || !startNav) return 0;
    
    return ((endNav.netAssetValue - startNav.netAssetValue) / startNav.netAssetValue) * 100;
  }

  private calculateDeviationFromMA(date: Date, period: number): number {
    // 简化实现，计算与移动平均的偏离
    const nav = this.getNavOnDate(date);
    if (!nav) return 0;
    
    // 这里应该计算真实的移动平均，暂时简化
    const ma = nav.netAssetValue * (0.95 + Math.random() * 0.1); // 模拟移动平均
    return ((nav.netAssetValue - ma) / ma) * 100;
  }

  private calculatePerformance(timeline: BacktestResult['timeline']) {
    if (timeline.length === 0) {
      return {
        totalReturn: 0,
        annualizedReturn: 0,
        volatility: 0,
        sharpeRatio: 0,
        maxDrawdown: 0,
        totalInvestment: 0,
        finalValue: 0
      };
    }

    const firstEntry = timeline[0];
    const lastEntry = timeline[timeline.length - 1];
    
    const totalReturn = ((lastEntry.value - lastEntry.totalInvestment) / lastEntry.totalInvestment) * 100;
    const years = (new Date(lastEntry.date).getTime() - new Date(firstEntry.date).getTime()) / (365.25 * 24 * 60 * 60 * 1000);
    const annualizedReturn = Math.pow(lastEntry.value / lastEntry.totalInvestment, 1 / years) - 1;
    
    // 计算波动率
    const returns = timeline.slice(1).map((entry, i) => 
      (entry.value - timeline[i].value) / timeline[i].value
    );
    const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const variance = returns.reduce((sum, r) => sum + Math.pow(r - avgReturn, 2), 0) / returns.length;
    const volatility = Math.sqrt(variance) * Math.sqrt(252) * 100; // 年化波动率
    
    // 计算最大回撤
    let maxDrawdown = 0;
    let peak = timeline[0].value;
    for (const entry of timeline) {
      if (entry.value > peak) {
        peak = entry.value;
      }
      const drawdown = (peak - entry.value) / peak;
      if (drawdown > maxDrawdown) {
        maxDrawdown = drawdown;
      }
    }

    const sharpeRatio = volatility > 0 ? (annualizedReturn * 100 - 3) / volatility : 0; // 假设无风险利率3%

    return {
      totalReturn,
      annualizedReturn: annualizedReturn * 100,
      volatility,
      sharpeRatio,
      maxDrawdown: maxDrawdown * 100,
      totalInvestment: lastEntry.totalInvestment,
      finalValue: lastEntry.value
    };
  }
}
