'use client';

import { useState } from 'react';
import { Strategy, StrategyType } from '@/types/fund';
import { getStrategies } from '@/lib/strategies';

interface StrategySelectorProps {
  selectedStrategy: Strategy | null;
  onStrategySelect: (strategy: Strategy) => void;
}

export default function StrategySelector({ selectedStrategy, onStrategySelect }: StrategySelectorProps) {
  const [expandedStrategy, setExpandedStrategy] = useState<string | null>(null);
  const strategies = getStrategies();

  const getRiskLevelColor = (level: Strategy['riskLevel']) => {
    const colors = {
      low: 'bg-green-100 text-green-800',
      medium: 'bg-yellow-100 text-yellow-800',
      high: 'bg-red-100 text-red-800'
    };
    return colors[level];
  };

  const getRiskLevelLabel = (level: Strategy['riskLevel']) => {
    const labels = {
      low: '低风险',
      medium: '中风险',
      high: '高风险'
    };
    return labels[level];
  };

  const getComplexityColor = (complexity: Strategy['complexity']) => {
    const colors = {
      beginner: 'bg-blue-100 text-blue-800',
      intermediate: 'bg-purple-100 text-purple-800',
      advanced: 'bg-gray-100 text-gray-800'
    };
    return colors[complexity];
  };

  const getComplexityLabel = (complexity: Strategy['complexity']) => {
    const labels = {
      beginner: '初级',
      intermediate: '中级',
      advanced: '高级'
    };
    return labels[complexity];
  };

  const toggleExpanded = (strategyId: string) => {
    setExpandedStrategy(expandedStrategy === strategyId ? null : strategyId);
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900">选择投资策略</h3>
      
      <div className="space-y-3">
        {strategies.map((strategy) => (
          <div
            key={strategy.id}
            className={`border rounded-lg transition-all duration-200 ${
              selectedStrategy?.id === strategy.id
                ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200'
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            {/* 策略头部 */}
            <div
              className="p-4 cursor-pointer"
              onClick={() => onStrategySelect(strategy)}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <h4 className="font-medium text-gray-900">{strategy.name}</h4>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getRiskLevelColor(strategy.riskLevel)}`}>
                      {getRiskLevelLabel(strategy.riskLevel)}
                    </span>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getComplexityColor(strategy.complexity)}`}>
                      {getComplexityLabel(strategy.complexity)}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{strategy.description}</p>
                  
                  {/* 展开/收起按钮 */}
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleExpanded(strategy.id);
                    }}
                    className="text-sm text-blue-600 hover:text-blue-800 flex items-center gap-1"
                  >
                    {expandedStrategy === strategy.id ? '收起详情' : '查看详情'}
                    <svg 
                      className={`w-4 h-4 transition-transform ${expandedStrategy === strategy.id ? 'rotate-180' : ''}`}
                      fill="none" 
                      stroke="currentColor" 
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>
                </div>
                
                {selectedStrategy?.id === strategy.id && (
                  <div className="ml-3 flex-shrink-0">
                    <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                      <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* 策略详情 */}
            {expandedStrategy === strategy.id && (
              <div className="px-4 pb-4 border-t border-gray-200">
                <div className="pt-4">
                  <h5 className="font-medium text-gray-900 mb-3">策略参数说明</h5>
                  <div className="space-y-3">
                    {Object.entries(strategy.parameterSchema).map(([key, param]) => (
                      <div key={key} className="bg-white p-3 rounded border">
                        <div className="flex items-start justify-between mb-1">
                          <span className="font-medium text-sm text-gray-900">{param.label}</span>
                          {param.required && (
                            <span className="text-xs text-red-500">必填</span>
                          )}
                        </div>
                        {param.description && (
                          <p className="text-xs text-gray-600 mb-2">{param.description}</p>
                        )}
                        <div className="text-xs text-gray-500">
                          <span>类型: {param.type}</span>
                          {param.min !== undefined && <span className="ml-2">最小值: {param.min}</span>}
                          {param.max !== undefined && <span className="ml-2">最大值: {param.max}</span>}
                          {param.defaultValue !== undefined && <span className="ml-2">默认值: {param.defaultValue}</span>}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* 选中的策略信息 */}
      {selectedStrategy && (
        <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="font-medium text-blue-900 mb-2">已选择策略</h4>
          <div className="text-sm text-blue-800">
            <p><span className="font-medium">名称:</span> {selectedStrategy.name}</p>
            <p><span className="font-medium">风险等级:</span> {getRiskLevelLabel(selectedStrategy.riskLevel)}</p>
            <p><span className="font-medium">复杂度:</span> {getComplexityLabel(selectedStrategy.complexity)}</p>
            <p><span className="font-medium">描述:</span> {selectedStrategy.description}</p>
          </div>
        </div>
      )}
    </div>
  );
}
