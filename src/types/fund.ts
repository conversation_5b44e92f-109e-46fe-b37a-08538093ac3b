// 基金相关类型定义

export interface Fund {
  id: string;
  name: string;
  code: string;
  type: 'stock' | 'bond' | 'hybrid' | 'index' | 'money';
  indexId?: string; // 关联的指数ID
  description?: string;
}

export interface Index {
  id: string;
  name: string;
  code: string;
  description?: string;
}

// 投资策略类型
export type StrategyType = 
  | 'fixed_amount'      // 定投
  | 'value_averaging'   // 价值平均
  | 'smart_fixed'       // 智能定投
  | 'grid_trading'      // 网格交易
  | 'momentum'          // 动量策略
  | 'mean_reversion';   // 均值回归

// 策略参数基础接口
export interface BaseStrategyParams {
  startDate: string;
  endDate: string;
  initialAmount: number;
}

// 定投策略参数
export interface FixedAmountParams extends BaseStrategyParams {
  monthlyAmount: number;
  frequency: 'weekly' | 'monthly' | 'quarterly';
}

// 价值平均策略参数
export interface ValueAveragingParams extends BaseStrategyParams {
  targetGrowthRate: number; // 目标增长率 (%)
  maxInvestment: number;    // 单次最大投资额
  frequency: 'monthly' | 'quarterly';
}

// 智能定投策略参数
export interface SmartFixedParams extends BaseStrategyParams {
  baseAmount: number;       // 基础投资额
  adjustmentFactor: number; // 调整系数
  valuationMetric: 'pe' | 'pb' | 'rsi'; // 估值指标
  frequency: 'monthly';
}

// 网格交易策略参数
export interface GridTradingParams extends BaseStrategyParams {
  gridCount: number;        // 网格数量
  priceRange: {
    min: number;
    max: number;
  };
  investmentPerGrid: number;
}

// 动量策略参数
export interface MomentumParams extends BaseStrategyParams {
  lookbackPeriod: number;   // 回看期间（天）
  threshold: number;        // 动量阈值 (%)
  rebalanceFrequency: 'monthly' | 'quarterly';
}

// 均值回归策略参数
export interface MeanReversionParams extends BaseStrategyParams {
  movingAveragePeriod: number; // 移动平均期间（天）
  deviationThreshold: number;  // 偏离阈值 (%)
  rebalanceFrequency: 'weekly' | 'monthly';
}

// 联合策略参数类型
export type StrategyParams = 
  | FixedAmountParams
  | ValueAveragingParams
  | SmartFixedParams
  | GridTradingParams
  | MomentumParams
  | MeanReversionParams;

// 策略定义
export interface Strategy {
  id: StrategyType;
  name: string;
  description: string;
  riskLevel: 'low' | 'medium' | 'high';
  complexity: 'beginner' | 'intermediate' | 'advanced';
  parameterSchema: {
    [key: string]: {
      type: 'number' | 'string' | 'select' | 'date' | 'range';
      label: string;
      required: boolean;
      min?: number;
      max?: number;
      step?: number;
      options?: { value: string; label: string }[];
      defaultValue?: any;
      description?: string;
    };
  };
}

// 回测结果
export interface BacktestResult {
  strategy: StrategyType;
  fund: Fund;
  params: StrategyParams;
  performance: {
    totalReturn: number;        // 总收益率 (%)
    annualizedReturn: number;   // 年化收益率 (%)
    volatility: number;         // 波动率 (%)
    sharpeRatio: number;        // 夏普比率
    maxDrawdown: number;        // 最大回撤 (%)
    totalInvestment: number;    // 总投入
    finalValue: number;         // 最终价值
  };
  timeline: {
    date: string;
    investment: number;         // 当期投入
    totalInvestment: number;    // 累计投入
    shares: number;             // 持有份额
    value: number;              // 当前价值
    return: number;             // 收益率 (%)
    netAssetValue: number;      // 基金净值
  }[];
}

// 基金历史数据
export interface FundData {
  date: string;
  netAssetValue: number;      // 净值
  accumulatedValue?: number;  // 累计净值
  volume?: number;            // 成交量
}

// 指数历史数据
export interface IndexData {
  date: string;
  value: number;              // 指数值
  pe?: number;                // 市盈率
  pb?: number;                // 市净率
  volume?: number;            // 成交量
}
