{
  "name": "fund-strategy",
  "version": "0.1.0",
  "private": true,
  "scripts": {
    "dev": "next dev --turbopack",
    "build": "next build",
    "start": "next start",
    "lint": "next lint"
  },
  "dependencies": {
    "date-fns": "^4.1.0",
    "next": "15.3.3",
    "react": "^19.1.0",
    "react-dom": "^19.1.0",
    "react-hook-form": "^7.57.0",
    "recharts": "^2.15.3"
  },
  "devDependencies": {
    "@eslint/eslintrc": "^3",
    "@tailwindcss/postcss": "^4",
    "@types/node": "^22",
    "@types/react": "^19",
    "@types/react-dom": "^19",
    "eslint": "^9",
    "eslint-config-next": "15.3.3",
        "eslint-config-prettier": "^10.1.5",
    "eslint-import-resolver-typescript": "^4.4.3",
    "eslint-plugin-import-x": "^4.15.1",
    "eslint-plugin-prettier": "^5.4.1",
    "eslint-plugin-promise": "^7.2.1",
    "eslint-plugin-sonarjs": "^3.0.2",
    "eslint-plugin-unicorn": "^59.0.1",
            "husky": "^9.1.7",
    "lint-staged": "^16.1.0",
    "prettier": "^3.5.3",
    "tailwindcss": "^4",
    "typescript": "^5.8.3",
    "typescript-eslint": "^8.33.1",
  }
}
