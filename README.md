# 基金投资策略回测计算器

一个基于 Next.js 和 React 19 构建的基金投资策略回测计算器，支持多种投资策略的历史表现分析。

## 功能特性

- 🎯 **多种投资策略**：支持定投、价值平均、智能定投、网格交易、动量策略、均值回归等策略
- 📊 **可视化图表**：使用 SVG 绘制投资收益走势图，直观展示回测结果
- 🔧 **动态参数配置**：根据选择的策略动态显示相应的参数输入界面
- 📱 **响应式设计**：使用 Tailwind CSS 构建，支持移动端和桌面端
- ⚡ **服务端渲染**：基于 Next.js 15，充分利用服务端渲染优势
- 🎨 **现代化 UI**：简洁美观的用户界面，良好的用户体验

## 技术栈

- **前端框架**：Next.js 15 + React 19
- **样式**：Tailwind CSS 4
- **语言**：TypeScript
- **图表**：自定义 SVG 图表（可扩展为 Recharts）
- **状态管理**：React Hooks

## 安装和运行

### 1. 安装依赖

首先安装必要的依赖包：

```bash
# 使用 npm
npm install recharts date-fns react-hook-form

# 或使用 yarn
yarn add recharts date-fns react-hook-form

# 或使用 pnpm
pnpm add recharts date-fns react-hook-form
```

### 2. 启动开发服务器

```bash
npm run dev
# 或
yarn dev
# 或
pnpm dev
```

打开 [http://localhost:3000](http://localhost:3000) 查看应用。

## 项目结构

```
src/
├── app/                    # Next.js App Router
│   ├── layout.tsx         # 根布局
│   ├── page.tsx           # 主页面
│   └── globals.css        # 全局样式
├── components/            # React 组件
│   ├── FundSelector.tsx   # 基金选择器
│   ├── StrategySelector.tsx # 策略选择器
│   ├── ParameterInputs.tsx # 动态参数输入
│   └── BacktestChart.tsx  # 回测结果图表
├── lib/                   # 工具库
│   ├── strategies.ts      # 投资策略定义
│   ├── backtest.ts        # 回测计算引擎
│   └── mockData.ts        # 模拟数据生成
└── types/                 # TypeScript 类型定义
    └── fund.ts            # 基金相关类型
```

## 支持的投资策略

### 1. 定期定额投资（定投）
- **风险等级**：低
- **适用人群**：投资新手
- **特点**：每隔固定时间投入固定金额，适合长期投资

### 2. 价值平均策略
- **风险等级**：中
- **适用人群**：有一定投资经验
- **特点**：根据目标增长率调整投资金额，市场低迷时多投

### 3. 智能定投
- **风险等级**：中
- **适用人群**：有一定投资经验
- **特点**：根据市场估值调整投资金额，估值低时多投

### 4. 网格交易
- **风险等级**：高
- **适用人群**：高级投资者
- **特点**：在价格区间内设置多个买卖点，低买高卖

### 5. 动量策略
- **风险等级**：高
- **适用人群**：高级投资者
- **特点**：追踪价格趋势，在上涨趋势中增加投资

### 6. 均值回归策略
- **风险等级**：中
- **适用人群**：中级投资者
- **特点**：基于价格偏离均值的程度进行投资

## 使用说明

1. **选择基金**：从基金列表中选择要分析的基金
2. **选择策略**：选择投资策略并查看策略说明
3. **设置参数**：根据策略要求填写相应参数
4. **执行回测**：点击"开始回测"按钮执行分析
5. **查看结果**：分析回测结果，包括收益率、风险指标等

## 回测指标说明

- **总收益率**：整个投资期间的总收益百分比
- **年化收益率**：按年计算的平均收益率
- **最大回撤**：投资期间的最大亏损幅度
- **夏普比率**：风险调整后的收益指标
- **波动率**：投资收益的波动程度

## 数据说明

目前使用模拟数据进行演示，实际使用时可以：

1. 接入真实的基金数据 API
2. 连接金融数据提供商（如 Wind、同花顺等）
3. 使用历史数据文件

## 扩展功能

### 计划中的功能
- [ ] 接入真实基金数据 API
- [ ] 支持更多投资策略
- [ ] 添加策略对比功能
- [ ] 导出回测报告
- [ ] 用户账户和历史记录
- [ ] 移动端 App

### 技术优化
- [ ] 使用 Recharts 替换自定义图表
- [ ] 添加数据缓存机制
- [ ] 性能优化和代码分割
- [ ] 单元测试覆盖

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 许可证

MIT License
